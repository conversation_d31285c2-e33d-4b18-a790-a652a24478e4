import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { BrainCircuit, Search, Zap, Code, ArrowDown } from "lucide-react"
import SemanticSearchDemo from "@/app/components/semantic-search-demo"

export default function LandingPage() {
  return (
    <div className="flex flex-col min-h-dvh bg-background text-foreground">
      <header className="sticky top-0 z-50 w-full border-b border-terminal-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <Link href="#" className="flex items-center gap-2 font-bold text-lg" prefetch={false}>
            <BrainCircuit className="h-6 w-6 text-neon-cyan" />
            <span>Semantic Search</span>
          </Link>
          <nav className="hidden md:flex items-center gap-6 text-sm font-medium">
            <Link href="#features" className="hover:text-neon-cyan transition-colors" prefetch={false}>
              Features
            </Link>
            <Link href="#demo" className="hover:text-neon-cyan transition-colors" prefetch={false}>
              Demo
            </Link>
            <Link href="#tech" className="hover:text-neon-cyan transition-colors" prefetch={false}>
              Technology
            </Link>
          </nav>
          <Button asChild className="bg-neon-cyan text-black hover:bg-neon-cyan/80 font-bold">
            <Link href="https://cohere.com/" target="_blank" rel="noopener noreferrer">
              Powered by Cohere
            </Link>
          </Button>
        </div>
      </header>

      <main className="flex-1">
        {/* Hero Section */}
        <section className="relative w-full py-24 md:py-32 lg:py-40 text-center overflow-hidden">
          <div className="absolute inset-0 bg-grid-neon-cyan/10 [mask-image:linear-gradient(to_bottom,white_5%,transparent_80%)]"></div>
          <div className="container relative px-4 md:px-6">
            <div className="max-w-3xl mx-auto space-y-4">
              <h1 className="text-4xl font-bold tracking-tighter sm:text-5xl md:text-6xl text-shadow-glow-cyan animate-fade-in">
                Explore Knowledge, Semantically
              </h1>
              <p
                className="text-lg text-muted-foreground md:text-xl animate-fade-in"
                style={{ animationDelay: "0.2s" }}
              >
                An interactive demo showcasing the power of Cohere's text embeddings to go beyond keywords and
                understand meaning.
              </p>
              <div className="flex justify-center gap-4 animate-fade-in" style={{ animationDelay: "0.4s" }}>
                <Button asChild size="lg" className="bg-neon-green text-black hover:bg-neon-green/80 font-bold">
                  <Link href="#demo">
                    Try the Demo <ArrowDown className="ml-2 h-4 w-4" />
                  </Link>
                </Button>
                <Button
                  asChild
                  size="lg"
                  variant="outline"
                  className="border-neon-cyan text-neon-cyan hover:bg-neon-cyan/10 hover:text-neon-cyan bg-transparent"
                >
                  <Link href="https://docs.cohere.com/docs/embeddings" target="_blank" rel="noopener noreferrer">
                    Learn about Embeddings
                  </Link>
                </Button>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="w-full py-12 md:py-24 lg:py-32 bg-muted/50">
          <div className="container px-4 md:px-6">
            <div className="text-center space-y-2 mb-12">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Why Semantic Search?</h2>
              <p className="max-w-2xl mx-auto text-muted-foreground md:text-lg">
                Unlock deeper insights by searching based on conceptual meaning, not just keyword matches.
              </p>
            </div>
            <div className="mx-auto grid max-w-5xl gap-8 sm:grid-cols-1 md:grid-cols-3">
              <div className="flex flex-col items-center text-center gap-2">
                <div className="p-3 rounded-full bg-neon-cyan/10 border border-neon-cyan/20">
                  <Search className="h-8 w-8 text-neon-cyan" />
                </div>
                <h3 className="text-xl font-bold">Real-time Embeddings</h3>
                <p className="text-muted-foreground">
                  Instantly convert your queries into rich numerical representations (vectors) using Cohere's
                  state-of-the-art models.
                </p>
              </div>
              <div className="flex flex-col items-center text-center gap-2">
                <div className="p-3 rounded-full bg-neon-cyan/10 border border-neon-cyan/20">
                  <Zap className="h-8 w-8 text-neon-cyan" />
                </div>
                <h3 className="text-xl font-bold">Similarity Scoring</h3>
                <p className="text-muted-foreground">
                  Find the most relevant information by calculating the cosine similarity between your query vector and
                  the knowledge base.
                </p>
              </div>
              <div className="flex flex-col items-center text-center gap-2">
                <div className="p-3 rounded-full bg-neon-cyan/10 border border-neon-cyan/20">
                  <Code className="h-8 w-8 text-neon-cyan" />
                </div>
                <h3 className="text-xl font-bold">Vector Visualization</h3>
                <p className="text-muted-foreground">
                  Get a glimpse into the high-dimensional vector space and see a visual representation of your query's
                  embedding.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* Demo Section */}
        <section id="demo" className="w-full py-12 md:py-24 lg:py-32">
          <div className="container px-4 md:px-6">
            <div className="text-center space-y-2 mb-12">
              <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl">Interactive Demo</h2>
              <p className="max-w-2xl mx-auto text-muted-foreground md:text-lg">
                Enter a query below to see semantic search in action. The terminal will process your request and find
                the most relevant documents from its knowledge base.
              </p>
            </div>
            <SemanticSearchDemo />
            {/* Note: API key configuration is now handled via environment variables */}
          </div>
        </section>

        {/* Tech & Use Cases Section */}
        <section id="tech" className="w-full py-12 md:py-24 lg:py-32 bg-muted/50">
          <div className="container grid gap-12 px-4 md:px-6 lg:grid-cols-2">
            <div className="space-y-4">
              <h3 className="text-2xl font-bold">Technology Stack</h3>
              <p className="text-muted-foreground">This demo is built with a modern, high-performance stack:</p>
              <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                <li>
                  <span className="font-semibold text-foreground">Cohere API:</span> For generating world-class text
                  embeddings.
                </li>
                <li>
                  <span className="font-semibold text-foreground">Next.js:</span> For a robust, server-rendered React
                  application.
                </li>
                <li>
                  <span className="font-semibold text-foreground">Tailwind CSS:</span> For rapid, utility-first styling
                  and responsiveness.
                </li>
                <li>
                  <span className="font-semibold text-foreground">Vercel:</span> For seamless deployment and hosting.
                </li>
              </ul>
            </div>
            <div className="space-y-4">
              <h3 className="text-2xl font-bold">Potential Use Cases</h3>
              <p className="text-muted-foreground">Semantic search can revolutionize various applications:</p>
              <ul className="list-disc list-inside space-y-2 text-muted-foreground">
                <li>
                  <span className="font-semibold text-foreground">Intelligent Search:</span> For internal wikis,
                  documentation, or customer support knowledge bases.
                </li>
                <li>
                  <span className="font-semibold text-foreground">Recommendation Engines:</span> Suggesting articles,
                  products, or content based on conceptual similarity.
                </li>
                <li>
                  <span className="font-semibold text-foreground">Data Clustering & Analysis:</span> Grouping large
                  volumes of text data by topic or theme automatically.
                </li>
              </ul>
            </div>
          </div>
        </section>
      </main>

      <footer className="bg-terminal-bg-secondary border-t border-terminal-border">
        <div className="container py-6 text-center text-sm text-muted-foreground">
          <p>&copy; {new Date().getFullYear()} Semantic Search Demo. All Rights Reserved.</p>
          <p>This is a fictional project for demonstration purposes. Not affiliated with HSN or Cohere.</p>
        </div>
      </footer>
    </div>
  )
}
