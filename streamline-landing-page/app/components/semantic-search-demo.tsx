"use client"

import type React from "react"

import { useState, useRef, useCallback } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { cn } from "@/lib/utils"
import { BrainCircuit, Search, Cpu, Database, AlertTriangle } from "lucide-react"

// --- TYPES ---
type KnowledgeBaseItem = {
  id: number
  title: string
  content: string
  category: string
  embedding: number[] | null
}

type SearchResultItem = KnowledgeBaseItem & {
  similarity: number
}

// --- CONSTANTS ---
const COHERE_API_KEY = process.env.NEXT_PUBLIC_COHERE_API_KEY || "YOUR_COHERE_API_KEY" // Get from environment variable

const demoKnowledgeBase: KnowledgeBaseItem[] = [
  {
    id: 1,
    title: "AI Collaborative Development Tools",
    content:
      "Tools like <PERSON>ursor, Claude <PERSON>, and GitHub Copilot are changing software development by understanding code context to provide intelligent suggestions and boost efficiency.",
    category: "AI Development",
    embedding: null,
  },
  {
    id: 2,
    title: "Terminal Aesthetics & Retro UI",
    content:
      "Combining retro terminal design with modern web tech creates unique user experiences. ASCII art, phosphor green colors, and typewriter effects evoke a nostalgic yet modern feel.",
    category: "UI Design",
    embedding: null,
  },
  {
    id: 3,
    title: "Semantic Search Technology",
    content:
      "Semantic search understands user intent and context, going beyond keywords. Using embedding vectors and similarity calculations, it finds conceptually related content.",
    category: "Search Tech",
    embedding: null,
  },
  {
    id: 4,
    title: "Knowledge Graph Applications",
    content:
      "Knowledge graphs structure information as entities and relationships. In AI, they provide contextual understanding for reasoning and question-answering systems.",
    category: "Knowledge Mgmt",
    embedding: null,
  },
  {
    id: 5,
    title: "API Testing Best Practices",
    content:
      "Effective API testing covers functional, performance, and security aspects. Tools like Bruno and Postman help automate this process to ensure API stability and reliability.",
    category: "Software Testing",
    embedding: null,
  },
]

// --- API & HELPERS ---
async function generateEmbedding(text: string): Promise<number[]> {
  if (COHERE_API_KEY === "YOUR_COHERE_API_KEY") {
    console.warn("Using mock data. Please replace 'YOUR_COHERE_API_KEY' to use the actual Cohere API.")
    // Return a random vector of the correct dimension for mock purposes
    return Array.from({ length: 1024 }, () => Math.random() * 2 - 1)
  }

  const response = await fetch("https://api.cohere.ai/v1/embed", {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
      Authorization: `Bearer ${COHERE_API_KEY}`,
    },
    body: JSON.stringify({
      texts: [text],
      model: "embed-english-v3.0",
      input_type: "search_query",
    }),
  })

  if (!response.ok) {
    const errorBody = await response.json()
    throw new Error(`Cohere API Error: ${response.status} - ${errorBody.message || response.statusText}`)
  }

  const data = await response.json()
  if (data.embeddings && data.embeddings.length > 0) {
    return data.embeddings[0]
  }
  throw new Error("Invalid Cohere API response format")
}

function calculateCosineSimilarity(vec1: number[], vec2: number[]): number {
  if (!vec1 || !vec2 || vec1.length !== vec2.length) return 0
  let dotProduct = 0,
    norm1 = 0,
    norm2 = 0
  for (let i = 0; i < vec1.length; i++) {
    dotProduct += vec1[i] * vec2[i]
    norm1 += vec1[i] * vec1[i]
    norm2 += vec2[i] * vec2[i]
  }
  if (norm1 === 0 || norm2 === 0) return 0
  return dotProduct / (Math.sqrt(norm1) * Math.sqrt(norm2))
}

// --- SUB-COMPONENTS ---
const VectorVisualization = ({ embedding }: { embedding: number[] | null }) => {
  if (!embedding) return null
  return (
    <div className="mt-2 flex flex-wrap gap-0.5 max-h-24 overflow-hidden" aria-label="Embedding vector visualization">
      {embedding.slice(0, 400).map((val, i) => {
        let colorClass = "bg-terminal-text-secondary/50"
        if (val > 0.1) colorClass = "bg-neon-green/70"
        else if (val < -0.1) colorClass = "bg-red-500/70"
        return <div key={i} className={cn("h-1 w-1 rounded-full", colorClass)} title={`Value: ${val.toFixed(4)}`} />
      })}
    </div>
  )
}

const SimilarityBar = ({ score }: { score: number }) => {
  const percentage = (score * 100).toFixed(1)
  const colorClass =
    score > 0.4
      ? "from-neon-green/80 to-neon-green"
      : score > 0.25
        ? "from-yellow-500/80 to-yellow-400"
        : "from-red-600/80 to-red-500"
  return (
    <div className="w-full bg-terminal-border rounded-full h-1.5 my-2" title={`Similarity: ${percentage}%`}>
      <div
        className={cn("bg-gradient-to-r h-1.5 rounded-full", colorClass)}
        style={{ width: `${Math.max(2, Number.parseFloat(percentage))}%` }}
      />
    </div>
  )
}

// --- MAIN COMPONENT ---
export default function SemanticSearchDemo() {
  const [query, setQuery] = useState("")
  const [isSearching, setIsSearching] = useState(false)
  const [status, setStatus] = useState("Ready")
  const [error, setError] = useState<string | null>(null)
  const [results, setResults] = useState<SearchResultItem[]>([])
  const [searchCount, setSearchCount] = useState(0)
  const [currentEmbedding, setCurrentEmbedding] = useState<number[] | null>(null)
  const [knowledgeBase, setKnowledgeBase] = useState<KnowledgeBaseItem[]>(demoKnowledgeBase)
  const [showWelcome, setShowWelcome] = useState(true)
  const resultsRef = useRef<HTMLDivElement>(null)

  const performSearch = useCallback(
    async (currentQuery: string) => {
      if (!currentQuery.trim() || isSearching) return

      setIsSearching(true)
      setShowWelcome(false)
      setError(null)
      setResults([])
      setStatus("Generating embedding...")
      resultsRef.current?.scrollIntoView({ behavior: "smooth" })

      try {
        const queryEmbedding = await generateEmbedding(currentQuery)
        setCurrentEmbedding(queryEmbedding)
        setStatus("Calculating similarities...")

        const kbWithEmbeddings = await Promise.all(
          knowledgeBase.map(async (item) => {
            if (!item.embedding) {
              // In a real app, these would be pre-computed and stored
              const itemEmbedding = await generateEmbedding(item.content)
              return { ...item, embedding: itemEmbedding }
            }
            return item
          }),
        )
        setKnowledgeBase(kbWithEmbeddings)

        const searchResults = kbWithEmbeddings
          .map((item) => ({
            ...item,
            similarity: calculateCosineSimilarity(queryEmbedding, item.embedding!),
          }))
          .sort((a, b) => b.similarity - a.similarity)

        setResults(searchResults)
        setSearchCount((c) => c + 1)
        setStatus("Search complete")
      } catch (e: any) {
        console.error("Semantic search failed:", e)
        setError(`Search failed: ${e.message}. Please check your API key and network connection.`)
        setStatus("Search failed")
      } finally {
        setIsSearching(false)
      }
    },
    [isSearching, knowledgeBase],
  )

  const handleSearch = () => {
    performSearch(query)
  }

  const handleKeyPress = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") handleSearch()
  }

  return (
    <div className="font-mono bg-terminal-bg text-terminal-text border-2 border-terminal-border rounded-lg shadow-2xl shadow-neon-cyan/10 overflow-hidden w-full max-w-4xl mx-auto">
      <header className="bg-terminal-bg-secondary p-3 border-b border-terminal-border flex flex-wrap items-center justify-between gap-2 text-xs sm:text-sm">
        <h2 className="text-neon-cyan font-bold text-shadow-glow-cyan flex items-center gap-2">
          <BrainCircuit size={18} />
          <span>HSN CLI: Cohere Semantic Search</span>
        </h2>
        <div className="flex items-center gap-4 text-terminal-text-secondary">
          <span>
            Searches: <span className="text-neon-green font-semibold">{searchCount}</span>
          </span>
          <span>
            Status: <span className="text-neon-cyan font-semibold">{status}</span>
          </span>
          <span>
            Vectors: <span className="text-white font-semibold">1024d</span>
          </span>
        </div>
      </header>

      <div className="p-4 sm:p-6 space-y-6 max-h-[70vh] overflow-y-auto">
        {/* Search Input Section */}
        <section className="bg-neon-cyan/5 border border-neon-cyan/50 rounded-md p-4 shadow-lg shadow-neon-cyan/10">
          <h3 className="text-neon-cyan font-bold mb-3 text-shadow-glow-cyan flex items-center gap-2">
            <Search size={16} /> Semantic Query
          </h3>
          <div className="flex flex-col sm:flex-row items-center gap-3">
            <span className="text-neon-cyan font-bold hidden sm:block">hsn-cohere:~$</span>
            <Input
              type="text"
              value={query}
              onChange={(e) => setQuery(e.target.value)}
              onKeyPress={handleKeyPress}
              placeholder="Explore a concept or question..."
              className="flex-1 bg-terminal-bg border-terminal-border focus:border-neon-cyan focus:ring-neon-cyan placeholder:text-terminal-text-secondary/60"
              disabled={isSearching}
              aria-label="Semantic search input"
            />
            <Button
              onClick={handleSearch}
              disabled={isSearching || !query.trim()}
              className="w-full sm:w-auto bg-neon-green text-black font-bold hover:bg-neon-green/80 transition-all duration-200 shadow-md shadow-neon-green/20 hover:shadow-lg hover:shadow-neon-green/30"
            >
              {isSearching ? "Searching..." : "Generate & Search"}
            </Button>
          </div>
        </section>

        {/* Results Section */}
        <section
          ref={resultsRef}
          className="bg-neon-green/5 border border-neon-green/50 rounded-md p-4 min-h-[200px] shadow-lg shadow-neon-green/10"
        >
          <h3 className="text-neon-green font-bold mb-3 text-shadow-glow-green flex items-center gap-2">
            <Database size={16} /> Search Results
          </h3>
          <div className="space-y-3">
            {isSearching && (
              <div className="text-center text-terminal-text-secondary animate-pulse-subtle p-4">
                🤔 Performing semantic analysis...
              </div>
            )}

            {error && (
              <div className="bg-red-900/50 border border-red-500 text-red-300 p-3 rounded-md flex items-start gap-2">
                <AlertTriangle size={20} className="flex-shrink-0 mt-0.5" />
                <span>{error}</span>
              </div>
            )}

            {showWelcome && (
              <div className="text-terminal-text-secondary p-2 animate-fade-in">
                <p className="text-neon-green mb-2">Welcome to the Semantic Search Demo!</p>
                <p>Enter a query to explore the knowledge base using Cohere's text embeddings.</p>
                <p className="mt-2 text-xs">e.g., "AI development tools" or "retro UI design"</p>
              </div>
            )}

            {!isSearching && !showWelcome && results.length === 0 && !error && (
              <div className="text-center text-terminal-text-secondary p-4">
                No relevant results found. Try a different query.
              </div>
            )}

            {results.map((item, index) => (
              <div
                key={item.id}
                className="bg-black/20 border border-terminal-border p-3 rounded-md animate-fade-in"
                style={{ animationDelay: `${index * 100}ms` }}
              >
                <div className="flex justify-between items-start gap-2">
                  <h4 className="font-bold text-terminal-text">{item.title}</h4>
                  <span className="text-xs font-bold bg-neon-green text-black px-2 py-0.5 rounded-full flex-shrink-0">
                    {`${(item.similarity * 100).toFixed(1)}%`}
                  </span>
                </div>
                <SimilarityBar score={item.similarity} />
                <p className="text-sm text-terminal-text-secondary leading-relaxed">{item.content}</p>
              </div>
            ))}
          </div>
        </section>

        {/* Embedding Section */}
        <section className="bg-terminal-text-secondary/5 border border-terminal-text-secondary/50 rounded-md p-4 shadow-lg shadow-black/20">
          <h3 className="text-terminal-text-secondary font-bold mb-3 flex items-center gap-2">
            <Cpu size={16} /> Query Embedding Vector
          </h3>
          <div className="text-xs text-terminal-text-secondary mb-2">
            <span>
              Dimension: <span className="text-white">{currentEmbedding?.length || "N/A"}</span>
            </span>{" "}
            |{" "}
            <span>
              Model: <span className="text-white">embed-english-v3.0</span>
            </span>
          </div>
          <div className="bg-terminal-bg-secondary p-2 rounded text-xs text-terminal-text-secondary/80 break-all">
            {currentEmbedding
              ? `[${currentEmbedding
                  .slice(0, 10)
                  .map((v) => v.toFixed(3))
                  .join(", ")}, ...]`
              : "No embedding generated yet."}
          </div>
          <VectorVisualization embedding={currentEmbedding} />
        </section>
      </div>
    </div>
  )
}
