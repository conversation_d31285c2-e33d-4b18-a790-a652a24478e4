{"timestamp": "2025-07-26T07:58:01.717065", "services": {"basic_memory_mcp": {"status": "running", "data_dir": "/Users/<USER>/.basic-memory", "config_files": 2, "db_files": 1, "auto_start": true, "notes": "Warp 內建 MCP 工具，自動啟動"}, "mem0_ai": {"status": "ready", "data_dir": "/Users/<USER>/.mem0", "config_exists": true, "db_exists": true, "qdrant_exists": true, "venv_exists": true, "api_key_set": true, "can_test": true, "auto_start": false, "notes": "手動啟動，需要 API key"}, "tavily_search": {"status": "ready", "configured_in_warp": true, "config_path": "/Users/<USER>/Library/Application Support/dev.warp.Warp-Stable/mcp_config.json", "api_key_set": true, "npx_available": true, "auto_start": false, "notes": "手動啟動，需要 API key"}}, "overall_status": "partially_ready"}